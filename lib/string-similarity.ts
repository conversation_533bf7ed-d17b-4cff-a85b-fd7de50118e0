import { distance } from 'fastest-levenshtein';

export function normalizedSimilarity(strA: string, strB: string) {
  const dist = distance(strA, strB);
  const maxLen = Math.max(strA.length, strB.length);
  // Handle edge case if both strings are empty
  if (maxLen === 0) return 1;
  return 1 - (dist / maxLen);
}

export function areSimilar(strA: string, strB: string, similarityThreshold = 0.8) {
  // Normalize strings for comparison: lowercase and remove common business suffixes
  const normalize = (str: string) => {
    return str.toLowerCase()
      .replace(/\s+/g, ' ')
      .replace(/(restaurant|cafe|bar|gmbh|ag|ltd\.?|limited|inc\.?)$/i, '')
      .trim();
  };
  
  const sim = normalizedSimilarity(normalize(strA), normalize(strB));
  return sim >= similarityThreshold;
} 