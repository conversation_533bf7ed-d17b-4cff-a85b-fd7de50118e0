{"name": "bianchi-lead-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "migrate": "tsx migrations/run-migrations.ts", "setup-auth": "tsx migrations/setup-auth.ts", "clean-auth": "tsx migrations/clean-up-tables.ts"}, "dependencies": {"@auth/pg-adapter": "^1.8.0", "@google/earthengine": "^1.5.7", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-table": "^8.20.6", "@turf/turf": "^7.2.0", "@types/bcryptjs": "^2.4.6", "@types/papaparse": "^5.3.15", "@types/pg": "^8.11.11", "@types/turf": "^3.5.32", "@types/uuid": "^10.0.0", "@vercel/postgres": "^0.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.40.0", "fastest-levenshtein": "^1.0.16", "h3-js": "^4.2.1", "lucide-react": "^0.474.0", "next": "^15.3.0", "next-auth": "^4.24.11", "papaparse": "^5.5.2", "pg": "^8.13.3", "react": "19.0.0", "react-dom": "19.0.0", "react-icons": "^5.4.0", "recharts": "^2.15.1", "sonner": "^1.7.4", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@types/google.maps": "^3.58.1", "@types/node": "^20", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "eslint": "^8", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.3", "typescript": "^5"}, "overrides": {"@types/react": "19.0.8", "@types/react-dom": "19.0.3", "next-auth": {"@auth/core": "0.34.2"}}}