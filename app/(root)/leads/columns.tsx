"use client";

import { ColumnDef, FilterFnOption } from "@tanstack/react-table";
import {
  ArrowUpDown,
  MoreHorizontal,
  Star,
  Phone,
  Globe,
  Copy,
  Mail,
  User,
  Building,
  CheckCircle,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { Lead } from "@/types/lead";

// Helper function for consistent cell styling
const truncateCell = (content: any) => (
  <div className="truncate max-w-[200px]" title={String(content || "")}>
    {String(content || "")}
  </div>
);

export const columns: ColumnDef<Lead>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Name
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => truncateCell(row.getValue("name")),
    enableSorting: true,
    enableColumnFilter: true,
    enableHiding: true,
  },
  {
    accessorKey: "street_name",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Street
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const streetName = row.getValue("street_name");
      const streetNumber = row.original.street_number;
      return truncateCell(
        streetNumber ? `${streetName} ${streetNumber}` : streetName
      );
    },
    enableHiding: true,
  },
  {
    accessorKey: "postal_code",
    header: "PLZ",
    cell: ({ row }) => truncateCell(row.getValue("postal_code")),
    enableHiding: true,
  },
  {
    accessorKey: "city",
    header: "City",
    cell: ({ row }) => truncateCell(row.getValue("city")),
    enableHiding: true,
  },
  {
    accessorKey: "canton",
    header: "Canton",
    cell: ({ row }) => truncateCell(row.getValue("canton")),
    enableSorting: true,
    enableColumnFilter: true,
    filterFn: (row, id, value) => {
      if (!value || value === "") return true;
      return row.getValue(id) === value;
    },
    enableHiding: true,
  },
  {
    accessorKey: "country",
    header: "Country",
    cell: ({ row }) => truncateCell(row.getValue("country")),
    filterFn: (row, id, value) => {
      if (!value || value === "") return true;
      return row.getValue(id) === value;
    },
    enableHiding: true,
  },
  {
    accessorKey: "google_place_id",
    header: "Places ID",
    cell: ({ row }) => {
      const placeId = row.getValue("google_place_id");
      return placeId ? (
        <div className="flex items-center gap-2">
          <div
            className="font-mono text-sm truncate max-w-[150px]"
            title={String(placeId)}
          >
            {String(placeId)}
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-4 w-4 p-0"
            onClick={() => navigator.clipboard.writeText(String(placeId))}
          >
            <Copy className="h-3 w-3" />
            <span className="sr-only">Copy Places ID</span>
          </Button>
        </div>
      ) : null;
    },
    enableHiding: true,
  },
  {
    accessorKey: "source",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Source
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    filterFn: (row, id, value) => {
      if (!value || value === "") return true;
      return row.getValue(id) === value;
    },
    enableHiding: true,
  },
  {
    accessorKey: "business_status",
    header: "Status",
    cell: ({ row }) => (
      <div className="capitalize">
        {(row.getValue("business_status") as string)?.toLowerCase() ||
          "unknown"}
      </div>
    ),
    filterFn: (row, id, value) => {
      if (!value || value === "") return true;
      return row.getValue(id) === value;
    },
    enableHiding: true,
  },
  {
    accessorKey: "international_phone",
    header: "Phone",
    cell: ({ row }) => {
      const phone = row.getValue("international_phone");
      return phone ? (
        <div className="flex items-center truncate">
          <Phone className="shrink-0 mr-2 h-4 w-4" />
          <span className="truncate">{String(phone)}</span>
        </div>
      ) : null;
    },
    enableHiding: true,
  },
  {
    accessorKey: "website_uri",
    header: "Website",
    cell: ({ row }) => {
      const website = row.getValue("website_uri");
      return website ? (
        <div className="flex items-center truncate">
          <Globe className="shrink-0 mr-2 h-4 w-4" />
          <a
            href={website as string}
            target="_blank"
            rel="noopener noreferrer"
            className="truncate"
            title={website as string}
          >
            {(website as string) || ""}
          </a>
        </div>
      ) : null;
    },
    enableHiding: true,
  },
  {
    accessorKey: "rating",
    header: "Rating",
    cell: ({ row }) => {
      const ratingValue = row.getValue("rating");
      if (!ratingValue) return "-";

      const rating =
        typeof ratingValue === "string"
          ? parseFloat(ratingValue)
          : (ratingValue as number);

      return !isNaN(rating) ? (
        <div className="flex items-center">
          <Star className="mr-1 h-4 w-4 text-yellow-400" />
          {rating.toFixed(1)}
        </div>
      ) : (
        "-"
      );
    },
    enableHiding: true,
  },
  {
    accessorKey: "location",
    header: "Location",
    cell: ({ row }) => {
      const location = row.getValue("location") as {
        latitude: number;
        longitude: number;
      } | null;
      if (
        !location ||
        typeof location.latitude !== "number" ||
        typeof location.longitude !== "number"
      ) {
        return null;
      }
      return (
        <div
          className="font-mono text-sm truncate"
          title={`${location.latitude}, ${location.longitude}`}
        >
          {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
        </div>
      );
    },
    enableHiding: true,
  },
  {
    accessorKey: "types",
    header: "Types",
    cell: ({ row }) => {
      const types = row.getValue("types") as string[];
      return types?.length ? (
        <div className="truncate max-w-[200px]" title={types.join(", ")}>
          {types.join(", ")}
        </div>
      ) : null;
    },
    enableHiding: true,
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Created
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      return new Date(row.getValue("created_at")).toLocaleDateString();
    },
    filterFn: "timePeriod" as FilterFnOption<Lead>,
    enableHiding: true,
  },

  // ERP Fields
  {
    accessorKey: "approved",
    header: "Approved",
    cell: ({ row }) => {
      const approved = row.getValue("approved");
      return approved === true ? (
        <CheckCircle className="h-4 w-4 text-green-500" />
      ) : approved === false ? (
        <X className="h-4 w-4 text-red-500" />
      ) : null;
    },
    enableHiding: true,
  },
  {
    accessorKey: "address_number",
    header: "Address #",
    cell: ({ row }) => truncateCell(row.getValue("address_number")),
    enableHiding: true,
  },
  {
    accessorKey: "email",
    header: "Email",
    cell: ({ row }) => {
      const email = row.getValue("email");
      return email ? (
        <div className="flex items-center truncate">
          <Mail className="shrink-0 mr-2 h-4 w-4" />
          <a
            href={`mailto:${email}`}
            className="truncate"
            title={email as string}
          >
            {email as string}
          </a>
        </div>
      ) : null;
    },
    enableHiding: true,
  },
  {
    accessorKey: "contact_person_email",
    header: "Contact Email",
    cell: ({ row }) => {
      const email = row.getValue("contact_person_email");
      return email ? (
        <div className="flex items-center truncate">
          <Mail className="shrink-0 mr-2 h-4 w-4" />
          <a
            href={`mailto:${email}`}
            className="truncate"
            title={email as string}
          >
            {email as string}
          </a>
        </div>
      ) : null;
    },
    enableHiding: true,
  },
  {
    accessorKey: "contact_person_first_name",
    header: "Contact First Name",
    cell: ({ row }) => truncateCell(row.getValue("contact_person_first_name")),
    enableHiding: true,
  },
  {
    accessorKey: "contact_person_last_name",
    header: "Contact Last Name",
    cell: ({ row }) => truncateCell(row.getValue("contact_person_last_name")),
    enableHiding: true,
  },
  {
    accessorKey: "language_code",
    header: "Language Code",
    cell: ({ row }) => truncateCell(row.getValue("language_code")),
    enableHiding: true,
  },
  {
    accessorKey: "language_description",
    header: "Language",
    cell: ({ row }) => truncateCell(row.getValue("language_description")),
    enableHiding: true,
  },
  {
    accessorKey: "address_group",
    header: "Address Group",
    cell: ({ row }) => truncateCell(row.getValue("address_group")),
    enableHiding: true,
  },
  {
    accessorKey: "address_group_description",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Address Group Description
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const value = row.getValue("address_group_description");
      let className = "";

      if (value === "Kunde") {
        className = "text-green-600";
      } else if (value === "Werbekunde") {
        className = "text-red-600";
      } else if (!value) {
        className = "text-blue-600";
      }

      return (
        <div className={`font-medium ${className}`}>{truncateCell(value)}</div>
      );
    },
    filterFn: (row, id, value) => {
      if (!value || value === "") return true;
      const cellValue = row.getValue(id);

      // Special case for empty values
      if (value === "__empty__" && (!cellValue || cellValue === ""))
        return true;

      return cellValue === value;
    },
    enableSorting: true,
    enableColumnFilter: true,
    enableHiding: true,
  },
  {
    accessorKey: "representative1",
    header: "Representative 1",
    cell: ({ row }) => truncateCell(row.getValue("representative1")),
    enableHiding: true,
  },
  {
    accessorKey: "representative2",
    header: "Representative 2",
    cell: ({ row }) => truncateCell(row.getValue("representative2")),
    enableHiding: true,
  },
  {
    accessorKey: "parent_group",
    header: "Parent Group",
    cell: ({ row }) => truncateCell(row.getValue("parent_group")),
    enableHiding: true,
  },
  {
    accessorKey: "parent_group_description",
    header: "Parent Group Description",
    cell: ({ row }) => truncateCell(row.getValue("parent_group_description")),
    enableHiding: true,
  },
  {
    accessorKey: "group",
    header: "Group",
    cell: ({ row }) => truncateCell(row.getValue("group")),
    enableHiding: true,
  },
  {
    accessorKey: "group_description",
    header: "Group Description",
    cell: ({ row }) => truncateCell(row.getValue("group_description")),
    enableHiding: true,
  },
  {
    accessorKey: "business_type",
    header: "Business Type",
    cell: ({ row }) => truncateCell(row.getValue("business_type")),
    enableHiding: true,
  },
  {
    accessorKey: "business_type_description",
    header: "Business Type Description",
    cell: ({ row }) => truncateCell(row.getValue("business_type_description")),
    enableHiding: true,
  },
  {
    accessorKey: "salutation_number",
    header: "Salutation Number",
    cell: ({ row }) => truncateCell(row.getValue("salutation_number")),
    enableHiding: true,
  },
  {
    accessorKey: "salutation_description",
    header: "Salutation",
    cell: ({ row }) => truncateCell(row.getValue("salutation_description")),
    enableHiding: true,
  },
  {
    accessorKey: "erp_id",
    header: "ERP ID",
    cell: ({ row }) => truncateCell(row.getValue("erp_id")),
    enableHiding: true,
  },
  {
    accessorKey: "name1",
    header: "Name 1",
    cell: ({ row }) => truncateCell(row.getValue("name1")),
    enableHiding: true,
  },
  {
    accessorKey: "name2",
    header: "Name 2",
    cell: ({ row }) => truncateCell(row.getValue("name2")),
    enableHiding: true,
  },
  {
    accessorKey: "name3",
    header: "Name 3",
    cell: ({ row }) => truncateCell(row.getValue("name3")),
    enableHiding: true,
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const lead = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(lead.id.toString())}
            >
              Copy lead ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <a href={`/leads/${lead.id}`}>View details</a>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
