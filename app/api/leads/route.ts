import { NextResponse } from "next/server";
import { query } from "@/lib/db";

export async function GET() {
  try {
    // Use PostGIS ST_AsGeoJSON to convert the geography point to a GeoJSON object
    // This will make it easier to extract latitude and longitude
    // Use a simpler query first to avoid potential SQL keyword issues
    const leads = await query(`
      SELECT
        *,
        CASE
          WHEN location IS NOT NULL THEN
            json_build_object(
              'latitude', ST_Y(location::geometry),
              'longitude', ST_X(location::geometry)
            )
          ELSE NULL
        END as location_coords
      FROM "bianchi_leads"
      WHERE country = 'Switzerland'
    `);

    // Map the results to replace the PostGIS location with our extracted coordinates
    // and format dates
    const formattedLeads = leads.map((lead) => ({
      ...lead,
      location: lead.location_coords, // Use our extracted coordinates
      location_coords: undefined, // Remove the temporary field
      created_at: new Date(lead.created_at).toISOString(),
      updated_at: lead.updated_at
        ? new Date(lead.updated_at).toISOString()
        : null,
    }));

    console.log(
      "Sample lead location:",
      formattedLeads.length > 0 ? formattedLeads[0].location : "No leads found"
    );

    return NextResponse.json({ leads: formattedLeads });
  } catch (error) {
    console.error("Database error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
