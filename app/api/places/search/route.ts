import { NextResponse } from "next/server";

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY;
const GEOCODING_API_URL = "https://maps.googleapis.com/maps/api/geocode/json";
const PLACES_API_URL = "https://places.googleapis.com/v1/places:searchNearby";

interface PlacesRequestBody {
  locationRestriction: {
    circle: {
      center: {
        latitude: number;
        longitude: number;
      };
      radius: number;
    };
  };
  includedTypes: string[];
  maxResultCount: number;
  languageCode: string;
  textQuery?: string; // Make textQuery optional
}

interface PlaceResponse {
  id: string;
  displayName?: { text: string };
  formattedAddress?: string;
  businessStatus?: string;
  location?: { latitude: number; longitude: number };
  types?: string[];
  addressComponents?: any[];
  primaryTypeDisplayName?: { text: string };
}

export async function GET(request: Request) {
  try {
    // Log the full incoming request URL
    console.log("Incoming request URL:", request.url);

    const { searchParams } = new URL(request.url);
    const location = searchParams.get("location");
    const radius = searchParams.get("radius");
    const type = searchParams.get("type");
    const keyword = searchParams.get("keyword");
    const maxResults = searchParams.get("maxResults");

    // Log all environment variables (except sensitive values)
    console.log("Environment check:", {
      hasApiKey: !!GOOGLE_MAPS_API_KEY,
      keyLength: GOOGLE_MAPS_API_KEY?.length,
    });

    if (!location || !GOOGLE_MAPS_API_KEY) {
      console.log("Missing required parameters");
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // First get coordinates from location
    const geocodeUrl = new URL(GEOCODING_API_URL);
    geocodeUrl.searchParams.append("address", location);
    geocodeUrl.searchParams.append("key", GOOGLE_MAPS_API_KEY);

    console.log("Attempting geocoding request...");

    const geocodeResponse = await fetch(geocodeUrl.toString());
    const geocodeData = await geocodeResponse.json();

    console.log("Geocoding response status:", geocodeData.status);
    console.log("Geocoding response:", {
      hasResults: !!geocodeData.results?.length,
      status: geocodeData.status,
      error_message: geocodeData.error_message,
    });

    if (
      geocodeData.status !== "OK" ||
      !geocodeData.results?.[0]?.geometry?.location
    ) {
      console.error("Location not found");
      return NextResponse.json(
        { error: "Location not found" },
        { status: 400 }
      );
    }

    const { lat, lng } = geocodeData.results[0].geometry.location;
    console.log("Coordinates found:", { lat, lng });

    // New Places API request
    const placesRequestBody: PlacesRequestBody = {
      locationRestriction: {
        circle: {
          center: {
            latitude: lat,
            longitude: lng,
          },
          radius: parseFloat(radius || "1000"),
        },
      },
      includedTypes: [type || "restaurant"],
      maxResultCount: parseInt(maxResults || "20"),
      languageCode: "en",
    };

    if (keyword) {
      placesRequestBody["textQuery"] = keyword;
    }

    console.log("Places API request body:", placesRequestBody);

    const placesResponse = await fetch(PLACES_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": GOOGLE_MAPS_API_KEY,
        "X-Goog-FieldMask": [
          "places.id",
          "places.displayName",
          "places.formattedAddress",
          "places.businessStatus",
          "places.location",
          "places.types",
          "places.googleMapsUri",
          "places.addressComponents",
          "places.primaryTypeDisplayName",
        ].join(","),
      },
      body: JSON.stringify(placesRequestBody),
    });

    if (!placesResponse.ok) {
      console.error("Places API error:", await placesResponse.text());
      return NextResponse.json(
        { error: "Places service failed" },
        { status: 500 }
      );
    }

    const placesData = await placesResponse.json();

    // Helper functions to extract address components
    function findAddressComponent(components: any[], type: string) {
      return components.find((component) => component.types.includes(type));
    }

    function extractAddressData(addressComponents: any[]) {
      const streetNumber =
        findAddressComponent(addressComponents, "street_number")?.longText ||
        "";
      const streetName =
        findAddressComponent(addressComponents, "route")?.longText || "";
      const city =
        findAddressComponent(addressComponents, "locality")?.longText || "";
      const postalCode =
        findAddressComponent(addressComponents, "postal_code")?.longText || "";
      const canton =
        findAddressComponent(addressComponents, "administrative_area_level_1")
          ?.shortText || "";
      const country =
        findAddressComponent(addressComponents, "country")?.longText || "";

      return {
        street_number: streetNumber,
        street_name: streetName,
        city,
        postal_code: postalCode,
        canton,
        country,
        street_address: streetNumber
          ? `${streetName} ${streetNumber}`
          : streetName,
      };
    }

    // Transform the response to match our expected format
    const results = (placesData.places || []).map((place: PlaceResponse) => {
      const addressData = extractAddressData(place.addressComponents || []);

      return {
        place_id: place.id || `place_${Math.random()}`,
        name: place.displayName?.text || "Unknown",
        formatted_address: place.formattedAddress || "",
        business_status: place.businessStatus || "UNKNOWN",
        location: {
          latitude: place.location?.latitude || null,
          longitude: place.location?.longitude || null,
        },
        types: place.types || [],
        address_components: place.addressComponents || {},
        // Add extracted address fields
        street_name: addressData.street_name,
        street_number: addressData.street_number,
        street_address: addressData.street_address,
        city: addressData.city,
        postal_code: addressData.postal_code,
        canton: addressData.canton,
        country: addressData.country,
        primary_type: place.primaryTypeDisplayName?.text || "",
      };
    });

    return NextResponse.json({
      results,
      status: "OK",
      total: results.length,
    });
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { error: "Failed to fetch places" },
      { status: 500 }
    );
  }
}

// Helper function to extract canton from address components
function extractCanton(addressComponents: any[]): string {
  const adminArea = addressComponents.find((component) =>
    component.types.includes("administrative_area_level_1")
  );
  return adminArea?.longText || "";
}
